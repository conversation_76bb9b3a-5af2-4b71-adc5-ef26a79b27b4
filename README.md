# 多用途商务创业公司模板

这是一个使用 jQuery + Bootstrap 重写的现代化商务网站模板，具有响应式设计和丰富的交互效果。

## 项目结构

```
website1/
├── index.html          # 主HTML文件
├── styles.css          # 自定义CSS样式
├── script.js           # JavaScript交互逻辑
├── index1.html         # 原始HTML文件（参考）
└── README.md           # 项目说明文档
```

## 技术栈

- **HTML5**: 语义化标签，提供良好的结构
- **CSS3**: 现代CSS特性，包括Flexbox、Grid、动画等
- **Bootstrap 5.3.0**: 响应式框架，提供组件和布局系统
- **jQuery 3.6.0**: JavaScript库，简化DOM操作和事件处理
- **Font Awesome 6.0.0**: 图标库
- **Google Fonts**: 网络字体（Oswald, Source Han Sans CN）

## 功能特性

### 🎨 视觉效果
- 现代化的设计风格
- 平滑的滚动动画
- 悬停效果和过渡动画
- 响应式布局，适配各种设备

### 🚀 交互功能
- 打字机效果的标题动画
- 平滑滚动导航
- 动态内容加载（服务和团队信息）
- 滚动时的元素动画
- 返回顶部按钮
- 移动端友好的导航菜单

### 📱 响应式设计
- 桌面端优化显示
- 平板设备适配
- 手机端优化布局
- 触摸友好的交互元素

## 页面结构

### 1. 头部导航 (Header)
- 固定导航栏
- 品牌Logo
- 响应式菜单
- 平滑滚动锚点

### 2. 主页横幅 (Hero Section)
- 打字机动画效果
- 大标题和副标题
- 行动号召按钮
- 背景图片和渐变

### 3. 关于我们 (About Section)
- 公司介绍
- 特色功能列表
- 图文并茂的布局
- 动画效果

### 4. 服务内容 (Services Section)
- 6个主要服务项目
- 卡片式布局
- 图标和描述
- 悬停动画效果

### 5. 团队介绍 (Team Section)
- 团队成员展示
- 头像、姓名、职位
- 个人简介
- 交互动画

### 6. 联系我们 (Contact Section)
- 联系信息
- 二维码展示
- 社交媒体链接
- 版权信息

## 使用方法

1. **直接打开**: 在浏览器中打开 `index.html` 文件
2. **本地服务器**: 使用本地服务器运行项目以获得最佳体验
3. **自定义**: 修改 `styles.css` 和 `script.js` 来定制样式和功能

## 自定义指南

### 修改颜色主题
在 `styles.css` 文件的 `:root` 选择器中修改CSS变量：

```css
:root {
    --primary-color: #24262b;    /* 主色调 */
    --secondary-color: #ffb100;  /* 辅助色 */
    --text-color: #6f6f6f;       /* 文字颜色 */
    --light-bg: #ffffff;         /* 浅色背景 */
    --dark-bg: #24262b;          /* 深色背景 */
}
```

### 修改内容
在 `script.js` 文件中修改服务和团队数据：

```javascript
// 修改服务内容
const servicesData = [
    {
        icon: 'fas fa-laptop-code',
        title: '您的服务标题',
        description: '您的服务描述'
    }
    // ... 更多服务
];

// 修改团队信息
const teamData = [
    {
        name: '姓名',
        position: '职位',
        description: '个人简介',
        avatar: '头像文字'
    }
    // ... 更多团队成员
];
```

### 修改打字机动画文字
在 `script.js` 文件中修改 `textArray` 数组：

```javascript
const textArray = ["您的第一行文字", "您的第二行文字"];
```

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- 移动端浏览器

## 性能优化

- 使用CDN加载外部资源
- 图片懒加载
- CSS和JavaScript压缩
- 平滑滚动优化
- 响应式图片

## 部署建议

1. **静态托管**: 可以部署到 GitHub Pages、Netlify、Vercel 等平台
2. **CDN加速**: 建议使用CDN来加速资源加载
3. **HTTPS**: 确保使用HTTPS协议
4. **SEO优化**: 添加适当的meta标签和结构化数据

## 维护和更新

- 定期更新Bootstrap和jQuery版本
- 检查外部CDN链接的可用性
- 优化图片大小和格式
- 测试在不同设备和浏览器上的表现

## 许可证

本项目仅供学习和参考使用。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 邮箱: <EMAIL>
- 电话: 400-000-0000

---

**注意**: 这是基于原始 `index1.html` 文件重构的现代化版本，保持了原有的视觉效果和功能，同时提升了代码质量和用户体验。
