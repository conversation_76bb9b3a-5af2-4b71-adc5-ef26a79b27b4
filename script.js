// jQuery Document Ready
$(document).ready(function () {
  // Typing Animation
  const typedTextSpan = document.querySelector(".typed-text");
  const cursorSpan = document.querySelector(".cursor");

  const textArray = ["掌握创业创新领域", "强有力的话语权"];
  const typingDelay = 100;
  const erasingDelay = 50;
  const newTextDelay = 2000;
  let textArrayIndex = 0;
  let charIndex = 0;

  function type() {
    if (charIndex < textArray[textArrayIndex].length) {
      if (!cursorSpan.classList.contains("typing"))
        cursorSpan.classList.add("typing");
      typedTextSpan.textContent += textArray[textArrayIndex].charAt(charIndex);
      charIndex++;
      setTimeout(type, typingDelay);
    } else {
      cursorSpan.classList.remove("typing");
      setTimeout(erase, newTextDelay);
    }
  }

  function erase() {
    if (charIndex > 0) {
      if (!cursorSpan.classList.contains("typing"))
        cursorSpan.classList.add("typing");
      typedTextSpan.textContent = textArray[textArrayIndex].substring(
        0,
        charIndex - 1
      );
      charIndex--;
      setTimeout(erase, erasingDelay);
    } else {
      cursorSpan.classList.remove("typing");
      textArrayIndex++;
      if (textArrayIndex >= textArray.length) textArrayIndex = 0;
      setTimeout(type, typingDelay + 1100);
    }
  }

  // Start typing animation
  if (textArray.length) setTimeout(type, newTextDelay + 250);

  // Navbar scroll effect
  $(window).scroll(function () {
    if ($(window).scrollTop() > 50) {
      $(".navbar").addClass("scrolled");
    } else {
      $(".navbar").removeClass("scrolled");
    }

    // Back to top button
    if ($(window).scrollTop() > 300) {
      $("#backToTop").fadeIn();
    } else {
      $("#backToTop").fadeOut();
    }
  });

  // Smooth scrolling for navigation links
  $('.navbar-nav a[href^="#"]').on("click", function (e) {
    e.preventDefault();

    const target = $(this.getAttribute("href"));
    if (target.length) {
      $("html, body")
        .stop()
        .animate(
          {
            scrollTop: target.offset().top - 80,
          },
          600
        );

      // Update active nav link
      $(".navbar-nav .nav-link").removeClass("active");
      $(this).addClass("active");

      // Close mobile menu if open
      $(".navbar-collapse").collapse("hide");
    }
  });

  // Back to top button click
  $("#backToTop").on("click", function (e) {
    e.preventDefault();
    $("html, body").animate({ scrollTop: 0 }, 600);
  });

  // Contact button click
  $(".contact-btn").on("click", function (e) {
    e.preventDefault();
    $("html, body").animate(
      {
        scrollTop: $("#contact").offset().top - 80,
      },
      600
    );
  });

  // Load Services Data
  const servicesData = [
    {
      icon: "fas fa-laptop-code",
      title: "网站建设",
      description:
        "专业的网站建设服务，为您打造独特的在线形象，提升品牌价值和用户体验。",
    },
    {
      icon: "fas fa-mobile-alt",
      title: "移动应用开发",
      description:
        "原生移动应用开发，支持iOS和Android平台，为您的业务提供移动端解决方案。",
    },
    {
      icon: "fas fa-chart-line",
      title: "数字营销",
      description:
        "全方位的数字营销策略，包括SEO优化、社交媒体营销和内容营销等服务。",
    },
    {
      icon: "fas fa-cogs",
      title: "技术咨询",
      description:
        "专业的技术咨询服务，为您的项目提供最佳的技术解决方案和架构设计。",
    },
    {
      icon: "fas fa-shield-alt",
      title: "网络安全",
      description:
        "全面的网络安全解决方案，保护您的数据和系统免受各种网络威胁。",
    },
    {
      icon: "fas fa-cloud",
      title: "云服务",
      description:
        "专业的云计算服务，包括云迁移、云架构设计和云平台管理等服务。",
    },
  ];

  // Render Services
  function renderServices() {
    const container = $("#services-container");
    container.empty();

    servicesData.forEach((service, index) => {
      const serviceCard = `
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="service-card fade-in-up" style="animation-delay: ${
                      index * 0.1
                    }s">
                        <div class="service-icon">
                            <i class="${service.icon}"></i>
                        </div>
                        <h4 class="service-title">${service.title}</h4>
                        <p class="service-description">${
                          service.description
                        }</p>
                    </div>
                </div>
            `;
      container.append(serviceCard);
    });
  }

  // Load Team Data
  const teamData = [
    {
      name: "张总监",
      position: "技术总监",
      description:
        "拥有15年的技术开发经验，专注于企业级应用开发和技术架构设计。",
      avatar: "张",
    },
    {
      name: "李经理",
      position: "项目经理",
      description: "资深项目管理专家，擅长大型项目的规划、执行和团队协调管理。",
      avatar: "李",
    },
    {
      name: "王设计师",
      position: "UI/UX设计师",
      description:
        "创意设计师，专注于用户体验设计和品牌视觉设计，作品获得多项设计奖项。",
      avatar: "王",
    },
    {
      name: "陈工程师",
      position: "前端工程师",
      description:
        "前端技术专家，精通各种前端框架和技术，致力于打造优秀的用户界面。",
      avatar: "陈",
    },
  ];

  // Render Team
  function renderTeam() {
    const container = $("#team-container");
    container.empty();

    teamData.forEach((member, index) => {
      const teamCard = `
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="team-card fade-in-up" style="animation-delay: ${
                      index * 0.1
                    }s">
                        <div class="team-avatar">
                            ${member.avatar}
                        </div>
                        <h4 class="team-name">${member.name}</h4>
                        <p class="team-position">${member.position}</p>
                        <p class="team-description">${member.description}</p>
                    </div>
                </div>
            `;
      container.append(teamCard);
    });
  }

  // Initialize content
  renderServices();
  renderTeam();

  // Scroll animations
  function checkScroll() {
    $(".fade-in-up, .fade-in-left, .fade-in-right").each(function () {
      const elementTop = $(this).offset().top;
      const elementBottom = elementTop + $(this).outerHeight();
      const viewportTop = $(window).scrollTop();
      const viewportBottom = viewportTop + $(window).height();

      if (elementBottom > viewportTop && elementTop < viewportBottom) {
        $(this).addClass("animate");
      }
    });
  }

  // Check scroll on page load and scroll
  checkScroll();
  $(window).scroll(checkScroll);

  // Update active nav link on scroll
  $(window).scroll(function () {
    const scrollPos = $(window).scrollTop() + 100;

    $("section[id]").each(function () {
      const sectionTop = $(this).offset().top;
      const sectionBottom = sectionTop + $(this).outerHeight();
      const sectionId = $(this).attr("id");

      if (scrollPos >= sectionTop && scrollPos < sectionBottom) {
        $(".navbar-nav .nav-link").removeClass("active");
        $(`.navbar-nav .nav-link[href="#${sectionId}"]`).addClass("active");
      }
    });
  });

  // Add hover effects to service cards
  $(document).on("mouseenter", ".service-card", function () {
    $(this).find(".service-icon i").addClass("fa-bounce");
  });

  $(document).on("mouseleave", ".service-card", function () {
    $(this).find(".service-icon i").removeClass("fa-bounce");
  });

  // Add click effects to team cards
  $(document).on("click", ".team-card", function () {
    $(this).addClass("clicked");
    setTimeout(() => {
      $(this).removeClass("clicked");
    }, 300);
  });

  // Social icon hover effects
  $(".social-icon").hover(
    function () {
      $(this).find("i").addClass("fa-bounce");
    },
    function () {
      $(this).find("i").removeClass("fa-bounce");
    }
  );

  // Feature item animation on hover
  $(".feature-item").hover(
    function () {
      $(this).find("i").addClass("fa-pulse");
    },
    function () {
      $(this).find("i").removeClass("fa-pulse");
    }
  );

  // Initialize tooltips if Bootstrap tooltips are available
  if (typeof bootstrap !== "undefined" && bootstrap.Tooltip) {
    const tooltipTriggerList = [].slice.call(
      document.querySelectorAll('[data-bs-toggle="tooltip"]')
    );
    const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl);
    });
  }

  // Console welcome message
  console.log(
    "%c欢迎访问我们的网站！",
    "color: #24262b; font-size: 20px; font-weight: bold;"
  );
  console.log(
    "%c如果您对我们的服务感兴趣，请联系我们！",
    "color: #ffb100; font-size: 14px;"
  );

  // Performance optimization: Lazy loading for images
  if ("IntersectionObserver" in window) {
    const imageObserver = new IntersectionObserver((entries, observer) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const img = entry.target;
          img.src = img.dataset.src;
          img.classList.remove("lazy");
          imageObserver.unobserve(img);
        }
      });
    });

    document.querySelectorAll("img[data-src]").forEach((img) => {
      imageObserver.observe(img);
    });
  }

  // Add loading animation
  $(window).on("load", function () {
    $(".hero-section").addClass("loaded");
    setTimeout(() => {
      checkScroll();
    }, 500);
  });

  // Mobile menu close on outside click
  $(document).on("click", function (e) {
    if (!$(e.target).closest(".navbar").length) {
      $(".navbar-collapse").collapse("hide");
    }
  });

  // Prevent default behavior for empty links
  $('a[href="#"]').on("click", function (e) {
    e.preventDefault();
  });
});
