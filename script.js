// jQuery Document Ready
$(document).ready(function () {
  // Typing Animation
  const typedTextSpan = document.querySelector(".typed-text");
  const cursorSpan = document.querySelector(".cursor");

  const textArray = ["专业IT外包服务", "值得信赖的技术伙伴"];
  const typingDelay = 100;
  const erasingDelay = 50;
  const newTextDelay = 2000;
  let textArrayIndex = 0;
  let charIndex = 0;

  function type() {
    if (charIndex < textArray[textArrayIndex].length) {
      if (!cursorSpan.classList.contains("typing"))
        cursorSpan.classList.add("typing");
      typedTextSpan.textContent += textArray[textArrayIndex].charAt(charIndex);
      charIndex++;
      setTimeout(type, typingDelay);
    } else {
      cursorSpan.classList.remove("typing");
      setTimeout(erase, newTextDelay);
    }
  }

  function erase() {
    if (charIndex > 0) {
      if (!cursorSpan.classList.contains("typing"))
        cursorSpan.classList.add("typing");
      typedTextSpan.textContent = textArray[textArrayIndex].substring(
        0,
        charIndex - 1
      );
      charIndex--;
      setTimeout(erase, erasingDelay);
    } else {
      cursorSpan.classList.remove("typing");
      textArrayIndex++;
      if (textArrayIndex >= textArray.length) textArrayIndex = 0;
      setTimeout(type, typingDelay + 1100);
    }
  }

  // Start typing animation
  if (textArray.length) setTimeout(type, newTextDelay + 250);

  // Navbar scroll effect
  $(window).scroll(function () {
    if ($(window).scrollTop() > 50) {
      $(".navbar").addClass("scrolled");
    } else {
      $(".navbar").removeClass("scrolled");
    }

    // Back to top button
    if ($(window).scrollTop() > 300) {
      $("#backToTop").fadeIn();
    } else {
      $("#backToTop").fadeOut();
    }
  });

  // Instant scrolling for navigation links
  $('.navbar-nav a[href^="#"]').on("click", function (e) {
    e.preventDefault();

    const target = $(this.getAttribute("href"));
    if (target.length) {
      // Instant scroll without animation
      $("html, body").scrollTop(target.offset().top - 80);

      // Update active nav link
      $(".navbar-nav .nav-link").removeClass("active");
      $(this).addClass("active");

      // Close mobile menu if open
      $(".navbar-collapse").collapse("hide");
    }
  });

  // Back to top button click
  $("#backToTop").on("click", function (e) {
    e.preventDefault();
    $("html, body").animate({ scrollTop: 0 }, 0);
  });

  // Contact button click
  $(".contact-btn").on("click", function (e) {
    e.preventDefault();
    $("html, body").animate(
      {
        scrollTop: $("#contact").offset().top - 80,
      },
      0
    );
  });

  // Load Services Data
  const servicesData = [
    {
      image:
        "https://ccdn1.goodq.top/caches/a8b7ad1af402b31299d0c355b81b9d38/aHR0cHM6Ly81ZDI4NWM4MmNjNzVkLnQ3My5xaWZlaXllLmNvbS9xZnktY29udGVudC91cGxvYWRzLzIwMTkvMDcvOWQxNzA1MGE4NjE4NjAzYTYzZWEyZDcxMDg1MDBiYmMtMTUweDE1MC05MC53ZWJw.webp",
      title: "软件开发外包",
      description: "提供Web应用、桌面软件、企业级系统等定制开发服务",
    },
    {
      image:
        "https://ccdn1.goodq.top/caches/a8b7ad1af402b31299d0c355b81b9d38/aHR0cHM6Ly81ZDI4NWM4MmNjNzVkLnQ3My5xaWZlaXllLmNvbS9xZnktY29udGVudC91cGxvYWRzLzIwMTkvMDcvMjE3ZWI4MmRlMTNhYWU5ZDA0OTllMTAzZTVkNzI2NDctMTUweDE1MC05MC53ZWJw.webp",
      title: "移动应用开发",
      description: "iOS、Android原生应用及跨平台移动应用开发服务",
    },
    {
      image:
        "https://ccdn1.goodq.top/caches/a8b7ad1af402b31299d0c355b81b9d38/aHR0cHM6Ly81ZDI4NWM4MmNjNzVkLnQ3My5xaWZlaXllLmNvbS9xZnktY29udGVudC91cGxvYWRzLzIwMTkvMDcvNjA4MTA0OTM3MDEwNzgxYTk3MzdhNDgwNzg2NDYwMWMtMTUweDE1MC05MC53ZWJw.webp",
      title: "系统集成服务",
      description: "企业信息化系统集成、数据迁移及系统优化服务",
    },
    {
      image:
        "https://ccdn1.goodq.top/caches/a8b7ad1af402b31299d0c355b81b9d38/aHR0cHM6Ly81ZDI4NWM4MmNjNzVkLnQ3My5xaWZlaXllLmNvbS9xZnktY29udGVudC91cGxvYWRzLzIwMTkvMDcvMjE3ZWI4MmRlMTNhYWU5ZDA0OTllMTAzZTVkNzI2NDctMTUweDE1MC05MC53ZWJw.webp",
      title: "技术咨询服务",
      description: "技术架构设计、项目评估及数字化转型咨询服务",
    },
    {
      image:
        "https://ccdn1.goodq.top/caches/a8b7ad1af402b31299d0c355b81b9d38/aHR0cHM6Ly81ZDI4NWM4MmNjNzVkLnQ3My5xaWZlaXllLmNvbS9xZnktY29udGVudC91cGxvYWRzLzIwMTkvMDcvNjA4MTA0OTM3MDEwNzgxYTk3MzdhNDgwNzg2NDYwMWMtMTUweDE1MC05MC53ZWJw.webp",
      title: "运维支持服务",
      description: "系统运维、性能监控、故障排除及技术支持服务",
    },
    {
      image:
        "https://ccdn1.goodq.top/caches/a8b7ad1af402b31299d0c355b81b9d38/aHR0cHM6Ly81ZDI4NWM4MmNjNzVkLnQ3My5xaWZlaXllLmNvbS9xZnktY29udGVudC91cGxvYWRzLzIwMTkvMDcvOWQxNzA1MGE4NjE4NjAzYTYzZWEyZDcxMDg1MDBiYmMtMTUweDE1MC05MC53ZWJw.webp",
      title: "人员外派服务",
      description: "专业技术人员外派，为客户提供驻场开发服务",
    },
  ];

  // Render Services
  function renderServices() {
    const container = $("#services-container");
    container.empty();

    servicesData.forEach((service, index) => {
      const serviceCard = `
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="service-card fade-in-up" style="animation-delay: ${
                      index * 0.1
                    }s">
                        <div class="service-image">
                            <img src="${service.image}" alt="${
        service.title
      }" class="img-fluid">
                        </div>
                        <h4 class="service-title">${service.title}</h4>
                        <p class="service-description">${
                          service.description
                        }</p>
                    </div>
                </div>
            `;
      container.append(serviceCard);
    });
  }

  // Load Team Data
  const teamData = [
    {
      name: "张总监",
      position: "技术总监",
      description:
        "拥有15年软件开发经验，专注于企业级系统架构设计和技术团队管理。",
      avatar: "张",
    },
    {
      name: "李经理",
      position: "项目经理",
      description:
        "资深项目管理专家，擅长外包项目的需求分析、进度控制和质量管理。",
      avatar: "李",
    },
    {
      name: "王工程师",
      position: "高级开发工程师",
      description:
        "全栈开发专家，精通Java、Python、前端技术，具有丰富的外包项目经验。",
      avatar: "王",
    },
    {
      name: "陈工程师",
      position: "移动端开发工程师",
      description:
        "移动应用开发专家，精通iOS、Android开发，专注于移动端解决方案。",
      avatar: "陈",
    },
  ];

  // Render Team
  function renderTeam() {
    const container = $("#team-container");
    container.empty();

    teamData.forEach((member, index) => {
      const teamCard = `
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="team-card fade-in-up" style="animation-delay: ${
                      index * 0.1
                    }s">
                        <h4 class="team-name">${member.name}</h4>
                        <p class="team-position">${member.position}</p>
                        <p class="team-description">${member.description}</p>
                    </div>
                </div>
            `;
      container.append(teamCard);
    });
  }

  // Initialize content
  renderServices();
  renderTeam();

  // Scroll animations
  function checkScroll() {
    $(".fade-in-up, .fade-in-left, .fade-in-right").each(function () {
      const elementTop = $(this).offset().top;
      const elementBottom = elementTop + $(this).outerHeight();
      const viewportTop = $(window).scrollTop();
      const viewportBottom = viewportTop + $(window).height();

      if (elementBottom > viewportTop && elementTop < viewportBottom) {
        $(this).addClass("animate");
      }
    });
  }

  // Check scroll on page load and scroll
  checkScroll();
  $(window).scroll(checkScroll);

  // Update active nav link on scroll
  $(window).scroll(function () {
    const scrollPos = $(window).scrollTop() + 100;

    $("section[id]").each(function () {
      const sectionTop = $(this).offset().top;
      const sectionBottom = sectionTop + $(this).outerHeight();
      const sectionId = $(this).attr("id");

      if (scrollPos >= sectionTop && scrollPos < sectionBottom) {
        $(".navbar-nav .nav-link").removeClass("active");
        $(`.navbar-nav .nav-link[href="#${sectionId}"]`).addClass("active");
      }
    });
  });

  // Add hover effects to service cards
  $(document).on("mouseenter", ".service-card", function () {
    $(this).find(".service-icon i").addClass("fa-bounce");
  });

  $(document).on("mouseleave", ".service-card", function () {
    $(this).find(".service-icon i").removeClass("fa-bounce");
  });

  // Add click effects to team cards
  $(document).on("click", ".team-card", function () {
    $(this).addClass("clicked");
    setTimeout(() => {
      $(this).removeClass("clicked");
    }, 300);
  });

  // Social icon hover effects
  $(".social-icon").hover(
    function () {
      $(this).find("i").addClass("fa-bounce");
    },
    function () {
      $(this).find("i").removeClass("fa-bounce");
    }
  );

  // Feature item animation on hover
  $(".feature-item").hover(
    function () {
      $(this).find("i").addClass("fa-pulse");
    },
    function () {
      $(this).find("i").removeClass("fa-pulse");
    }
  );

  // Initialize tooltips if Bootstrap tooltips are available
  if (typeof bootstrap !== "undefined" && bootstrap.Tooltip) {
    const tooltipTriggerList = [].slice.call(
      document.querySelectorAll('[data-bs-toggle="tooltip"]')
    );
    const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl);
    });
  }

  // Console welcome message
  console.log(
    "%c欢迎访问我们的网站！",
    "color: #24262b; font-size: 20px; font-weight: bold;"
  );
  console.log(
    "%c如果您对我们的服务感兴趣，请联系我们！",
    "color: #ffb100; font-size: 14px;"
  );

  // Performance optimization: Lazy loading for images
  if ("IntersectionObserver" in window) {
    const imageObserver = new IntersectionObserver((entries, observer) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const img = entry.target;
          img.src = img.dataset.src;
          img.classList.remove("lazy");
          imageObserver.unobserve(img);
        }
      });
    });

    document.querySelectorAll("img[data-src]").forEach((img) => {
      imageObserver.observe(img);
    });
  }

  // Add loading animation
  $(window).on("load", function () {
    $(".hero-section").addClass("loaded");
    setTimeout(() => {
      checkScroll();
    }, 500);
  });

  // Mobile menu close on outside click
  $(document).on("click", function (e) {
    if (!$(e.target).closest(".navbar").length) {
      $(".navbar-collapse").collapse("hide");
    }
  });

  // Prevent default behavior for empty links
  $('a[href="#"]').on("click", function (e) {
    e.preventDefault();
  });
});
