/* Custom CSS for Business Template */

/* Global Styles */
:root {
  --primary-color: #24262b;
  --secondary-color: #ffb100;
  --text-color: #6f6f6f;
  --light-bg: #ffffff;
  --dark-bg: #24262b;
  --font-primary: "Source Han Sans CN", sans-serif;
  --font-secondary: "<PERSON>", sans-serif;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-primary);
  color: var(--text-color);
  line-height: 1.6;
  overflow-x: hidden;
}

/* Header Styles */
.header-section {
  position: relative;
  z-index: 1000;
}

.navbar {
  padding: 1rem 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.navbar.scrolled {
  padding: 0.5rem 0;
  background-color: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px);
}

.logo {
  max-height: 50px;
  width: auto;
}

.navbar-nav .nav-link {
  color: var(--primary-color) !important;
  font-weight: 500;
  margin: 0 1rem;
  transition: color 0.3s ease;
  position: relative;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
  color: var(--secondary-color) !important;
}

.navbar-nav .nav-link::after {
  content: "";
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -5px;
  left: 50%;
  background-color: var(--secondary-color);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after,
.navbar-nav .nav-link.active::after {
  width: 100%;
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  padding-top: 80px;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("https://ccdn1.goodq.top/caches/a8b7ad1af402b31299d0c355b81b9d38/aHR0cHM6Ly81ZDI4NWM4MmNjNzVkLnQ3My5xaWZlaXllLmNvbS9xZnktY29udGVudC91cGxvYWRzLzIwMTkvMDcvMjkxYTBiNjUyOTgwMjI1MWMxNWM2MGNmZWZiODk2NjUtOTAud2VicA_p_p100_p_3D_p_p100_p_3D.webp")
    no-repeat left top;
  background-size: auto;
  opacity: 0.1;
  z-index: -1;
}

.hero-content {
  padding: 2rem 0;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 1rem;
  font-family: var(--font-secondary);
  line-height: 1.2;
}

.hero-subtitle {
  font-size: 1.5rem;
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  font-weight: 600;
}

.hero-description {
  font-size: 1rem;
  color: var(--text-color);
  margin-bottom: 2rem;
  line-height: 1.8;
}

.hero-image img {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(0px);
  }
}

/* Typing Animation */
.typed-text {
  color: var(--primary-color);
}

.cursor {
  display: inline-block;
  background-color: var(--primary-color);
  margin-left: 0.1rem;
  width: 3px;
  animation: blink 1s infinite;
}

@keyframes blink {
  0% {
    background-color: var(--primary-color);
  }
  50% {
    background-color: transparent;
  }
  100% {
    background-color: var(--primary-color);
  }
}

/* Button Styles */
.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  padding: 12px 35px;
  font-size: 15px;
  font-weight: 500;
  border-radius: 0;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Section Styles */
.section-header {
  margin-bottom: 3rem;
}

.section-subtitle {
  font-family: var(--font-secondary);
  font-size: 1rem;
  color: var(--primary-color);
  text-transform: uppercase;
  letter-spacing: 1px;
  display: block;
  margin-bottom: 0.5rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 1rem;
  font-family: var(--font-primary);
}

.section-description {
  font-size: 1rem;
  color: var(--text-color);
  letter-spacing: 1px;
}

/* About Section */
.about-section {
  padding: 5rem 0;
  background-color: var(--light-bg);
  position: relative;
}

.about-section::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 50%;
  height: 100%;
  background: url("https://ccdn1.goodq.top/caches/a8b7ad1af402b31299d0c355b81b9d38/aHR0cHM6Ly81ZDI4NWM4MmNjNzVkLnQ3My5xaWZlaXllLmNvbS9xZnktY29udGVudC91cGxvYWRzLzIwMTkvMDcvMjkxYTBiNjUyOTgwMjI1MWMxNWM2MGNmZWZiODk2NjUtOTAud2VicA_p_p100_p_3D_p_p100_p_3D.webp")
    no-repeat left top;
  background-size: auto;
  opacity: 0.05;
  z-index: 0;
}

.about-content {
  position: relative;
  z-index: 1;
  padding: 2rem 0;
}

.about-description {
  font-size: 1rem;
  color: var(--text-color);
  margin-bottom: 1.5rem;
  line-height: 1.8;
}

.feature-list {
  margin: 2rem 0;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  padding: 0.5rem 0;
}

.feature-item i {
  color: var(--secondary-color);
  background-color: var(--secondary-color);
  color: white;
  width: 22px;
  height: 22px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  margin-right: 1rem;
  flex-shrink: 0;
}

.feature-item span {
  color: var(--text-color);
  font-size: 15px;
}

/* Services Section */
.services-section {
  padding: 5rem 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
}

.services-section::before {
  content: "";
  position: absolute;
  bottom: 0;
  right: 0;
  width: 50%;
  height: 100%;
  background: url("https://ccdn1.goodq.top/caches/a8b7ad1af402b31299d0c355b81b9d38/aHR0cHM6Ly81ZDI4NWM4MmNjNzVkLnQ3My5xaWZlaXllLmNvbS9xZnktY29udGVudC91cGxvYWRzLzIwMTkvMDcvMzU3OTg2ZWMwNWRkNzY3YjJlNGRlM2M0MmI5ZmU1OTItOTAud2VicA_p_p100_p_3D_p_p100_p_3D.webp")
    no-repeat right bottom;
  background-size: auto;
  opacity: 0.1;
  z-index: 0;
}

.services-section .section-subtitle,
.services-section .section-title,
.services-section .section-description {
  color: white;
}

.service-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  padding: 2rem;
  margin-bottom: 2rem;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  height: 100%;
}

.service-card:hover {
  transform: translateY(-10px);
  background: rgba(255, 255, 255, 0.2);
}

.service-icon {
  font-size: 3rem;
  color: var(--secondary-color);
  margin-bottom: 1.5rem;
}

.service-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: white;
}

.service-description {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
}

/* Team Section */
.team-section {
  padding: 5rem 0;
  background-color: #f8f9fa;
}

.team-card {
  background: white;
  border-radius: 10px;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  margin-bottom: 2rem;
  height: 100%;
}

.team-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.team-avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  margin: 0 auto 1.5rem;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  color: white;
}

.team-name {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.team-position {
  color: var(--secondary-color);
  font-weight: 500;
  margin-bottom: 1rem;
}

.team-description {
  color: var(--text-color);
  line-height: 1.6;
}

/* Contact Section */
.contact-section {
  padding: 5rem 0;
  background-color: var(--dark-bg);
  color: white;
}

.contact-section .section-subtitle,
.contact-section .section-title,
.contact-section .section-description {
  color: white;
}

.contact-info h4 {
  color: white;
  margin-bottom: 2rem;
  font-size: 1.5rem;
}

.contact-item {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  color: rgba(255, 255, 255, 0.9);
}

.contact-item i {
  color: var(--secondary-color);
  font-size: 1.2rem;
  margin-right: 1rem;
  width: 20px;
}

.qr-codes img {
  max-width: 80px;
  height: auto;
}

.qr-codes p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  margin-bottom: 0;
}

.social-icons {
  margin-bottom: 2rem;
}

.social-icon {
  display: inline-block;
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  text-align: center;
  line-height: 40px;
  margin: 0 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
  text-decoration: none;
}

.social-icon:hover {
  background-color: var(--secondary-color);
  color: white;
  transform: translateY(-3px);
}

.footer-links p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.copyright {
  font-size: 0.8rem !important;
  color: rgba(255, 255, 255, 0.6) !important;
}

/* Back to Top Button */
.back-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: none;
  z-index: 999;
  border: none;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.back-to-top:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.2rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .navbar-nav .nav-link {
    margin: 0.5rem 0;
  }

  .hero-section {
    padding-top: 100px;
    text-align: center;
  }

  .about-content,
  .hero-content {
    text-align: center;
    margin-bottom: 2rem;
  }
}

/* Animation Classes */
.fade-in-up {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
}

.fade-in-up.animate {
  opacity: 1;
  transform: translateY(0);
}

.fade-in-left {
  opacity: 0;
  transform: translateX(-30px);
  transition: all 0.6s ease;
}

.fade-in-left.animate {
  opacity: 1;
  transform: translateX(0);
}

.fade-in-right {
  opacity: 0;
  transform: translateX(30px);
  transition: all 0.6s ease;
}

.fade-in-right.animate {
  opacity: 1;
  transform: translateX(0);
}

/* Additional Animation Effects */
.team-card.clicked {
  transform: scale(0.95);
  transition: transform 0.1s ease;
}

.hero-section.loaded .hero-content {
  animation: slideInLeft 1s ease-out;
}

.hero-section.loaded .hero-image {
  animation: slideInRight 1s ease-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Loading States */
.service-card,
.team-card {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
}

.service-card.animate,
.team-card.animate {
  opacity: 1;
  transform: translateY(0);
}

/* Hover Effects */
.service-card:hover .service-icon {
  transform: scale(1.1);
  transition: transform 0.3s ease;
}

.team-card:hover .team-avatar {
  transform: scale(1.05);
  transition: transform 0.3s ease;
}

/* Lazy Loading */
img.lazy {
  opacity: 0;
  transition: opacity 0.3s;
}

img:not(.lazy) {
  opacity: 1;
}

/* Mobile Optimizations */
@media (max-width: 576px) {
  .hero-title {
    font-size: 2rem;
  }

  .section-title {
    font-size: 1.8rem;
  }

  .service-card,
  .team-card {
    margin-bottom: 1.5rem;
  }

  .contact-item {
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
  }

  .contact-item i {
    margin-bottom: 0.5rem;
  }
}
